name: Goofish API Server Deploy

on:
  push:
    branches:
      - main

env:
  SERVICE_NAME: 闲管家虚拟货源 API
  DEPLOY_ENV: test
  APP_VERSION: 1.0.0

jobs:
  vars:
    runs-on: ubuntu-22.04
    outputs:
      service-name: ${{ env.SERVICE_NAME }}
      service-url: ${{ vars.TEST_MANAGEMENT_URL }}
      version: ${{ steps.get-version.outputs.version }}
      app-version: ${{ env.APP_VERSION }}
    steps:
      - uses: kokoroX/get-version-action@main
        id: get-version

  build-and-deploy:
    needs: [vars]
    runs-on: ubuntu-22.04
    environment: taidong
    env:
      CGO_ENABLED: 0
      GOOS: linux
      GOARCH: amd64
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: https://registry.npmjs.org/

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.3'
          check-latest: true
          cache-dependency-path: go.sum

      - name: Install Serverless Devs
        run: npm install -g @serverless-devs/s

      - name: Install dependencies
        run: go mod download

      - name: Install protobuf plugins
        run: |
          # Ensure Go bin directory is in PATH
          echo "$(go env GOPATH)/bin" >> $GITHUB_PATH

          # Install required protobuf plugins
          go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
          go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
          go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
          go install github.com/go-kratos/kratos/cmd/protoc-gen-go-errors/v2@latest
          go install github.com/google/gnostic/cmd/protoc-gen-openapi@latest
          go install github.com/envoyproxy/protoc-gen-validate@latest
          go install github.com/bufbuild/buf/cmd/buf@latest

          # Verify installations
          which protoc-gen-go
          which protoc-gen-go-grpc
          which buf

      - name: Generate API code
        run: |
          cd api

          # Check if buf.yaml exists
          if [ ! -f "buf.yaml" ]; then
            echo "Error: buf.yaml not found in api directory"
            exit 1
          fi

          # Update buf dependencies
          buf dep update

          # Generate API code
          buf generate

          # Verify generated files
          if [ -d "../app/admin/service/cmd/server/assets" ]; then
            echo "Generated files:"
            ls -la ../app/admin/service/cmd/server/assets/
          fi

      - name: Build admin service
        run: |
          cd app/admin/service
          mkdir -p bin
          CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
            -ldflags "-X main.version=${{ needs.vars.outputs.app-version }}" \
            -o bin/server \
            ./cmd/server

      - name: Prepare deployment files
        run: |
          # Create main executable for serverless deployment
          cp app/admin/service/bin/server main

          # Prepare config files in the root directory
          if [ -d "app/admin/service/configs" ]; then
            mkdir -p configs
            cp -r app/admin/service/configs/* configs/
            echo "Copied config files:"
            ls -la configs/
          fi

          # Copy assets directory if it exists
          if [ -d "app/admin/service/cmd/server/assets" ]; then
            mkdir -p assets
            cp -r app/admin/service/cmd/server/assets/* assets/
            echo "Copied asset files:"
            ls -la assets/
          fi

          # Copy data directory for SQLite database
          if [ -d "app/admin/service/data" ]; then
            mkdir -p data
            cp -r app/admin/service/data/* data/
            echo "Copied data files:"
            ls -la data/
          fi

      - name: Configure Serverless Devs
        run: s config add --AccessKeyID ${{secrets.ALIYUN_ACCESS_KEY_ID}} --AccessKeySecret ${{secrets.ALIYUN_ACCESS_KEY_SECRET}} -a tdid -f

      - name: Deploy to Serverless
        run: s deploy -y --use-local -t s.${{ env.DEPLOY_ENV }}.yaml


  feishu-success-notice:
    needs: [vars, build-and-deploy]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.success.notice.yml@main
    secrets: inherit
    with:
      version: ${{ needs.vars.outputs.version }}
      service-name: ${{ needs.vars.outputs.service-name }}
      service-url: ${{ needs.vars.outputs.service-url }}
      environment-name: 钛动环境


  feishu-failure-notice:
    if: failure()
    needs: [vars, build-and-deploy]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.failure.notice.yml@main
    secrets: inherit
    with:
      service-name: ${{ needs.vars.outputs.service-name }}
      version: ${{ needs.vars.outputs.version }}
      environment-name: 钛动环境